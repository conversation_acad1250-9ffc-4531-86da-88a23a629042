/**
 * 矩阵主组件
 * 🎯 核心价值：纯渲染组件，数据驱动视图，零业务逻辑
 * 📦 功能范围：矩阵渲染、交互事件传递、性能优化
 * 🔄 架构设计：完全无状态组件，所有逻辑通过props注入
 */

'use client';

import { matrixCore } from '@/core/matrix/MatrixCore';
import { useMatrixConfig, useMatrixData, useMatrixStore } from '@/core/matrix/MatrixStore';
import type {
  BusinessMode,
  Coordinate,
  MatrixConfig,
} from '@/core/matrix/MatrixTypes';
import { MATRIX_SIZE } from '@/core/matrix/MatrixTypes';
import React, { memo, useCallback, useEffect, useRef, useState } from 'react';

import { createInteractionEvent } from '@/core/matrix/MatrixCore';

// ===== 组件属性 =====

interface MatrixProps {
  /** 自定义配置覆盖 */
  configOverride?: Partial<MatrixConfig>;

  /** 容器样式 */
  className?: string;
  style?: React.CSSProperties;

  /** 交互事件回调 */
  onCellClick?: (coordinate: Coordinate, event: React.MouseEvent) => void;
  onCellDoubleClick?: (coordinate: Coordinate, event: React.MouseEvent) => void;
  onCellHover?: (coordinate: Coordinate, event: React.MouseEvent) => void;
  onCellFocus?: (coordinate: Coordinate, event: React.FocusEvent) => void;
  onModeChange?: (mode: BusinessMode) => void;
}

// ===== 主组件 =====

const MatrixComponent: React.FC<MatrixProps> = ({
  configOverride,
  className = '',
  style,
  onCellClick,
  onCellDoubleClick,
  onCellHover,
  onCellFocus,
  onModeChange,
}) => {
  const containerRef = useRef<HTMLDivElement>(null);
  const isInitialized = useRef(false);
  const [isClient, setIsClient] = useState(false);

  // 获取状态
  const matrixData = useMatrixData();
  const matrixConfig = useMatrixConfig();
  const {
    initializeMatrix,
    selectCell,
    hoverCell,
    focusCell,
    getCellRenderData,
  } = useMatrixStore();

  // 合并配置
  const finalConfig = { ...matrixConfig, ...configOverride };

  // 确保客户端渲染一致性
  useEffect(() => {
    setIsClient(true);
  }, []);

  // 初始化
  useEffect(() => {
    if (!isInitialized.current && containerRef.current && isClient) {
      // 初始化矩阵数据
      if (matrixData.cells.size === 0) {
        initializeMatrix();
      }
      isInitialized.current = true;
    }
  }, [initializeMatrix, matrixData.cells.size, isClient]);

  // 渲染矩阵单元格
  const renderMatrixCells = useCallback(() => {
    const cells = [];
    for (let y = 0; y < MATRIX_SIZE; y++) {
      for (let x = 0; x < MATRIX_SIZE; x++) {
        const cellRenderData = getCellRenderData(x, y);
        const key = `${x},${y}`;

        cells.push(
          <div
            key={key}
            data-x={x}
            data-y={y}
            className={cellRenderData?.className || 'matrix-cell'}
            style={{
              position: 'absolute',
              left: `${x * 34}px`, // 40px + 1px gap
              top: `${y * 34}px`,
              width: '33px',
              height: '33px',
              border: '1px solid #e5e7eb',
              backgroundColor: cellRenderData?.style?.backgroundColor || '#ffffff',
              color: cellRenderData?.style?.color || '#000000',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              cursor: 'pointer',
              userSelect: 'none',
              overflow: 'hidden',
              textOverflow: 'ellipsis',
              whiteSpace: 'nowrap',
              borderRadius: '6px', // 统一圆角
              ...cellRenderData?.style, // 动态样式（包括fontSize）应该最后应用
            }}
          >
            {cellRenderData?.content || ''}
          </div>
        );
      }
    }
    return cells;
  }, [getCellRenderData]);

  // 处理单元格点击
  const handleCellClick = useCallback((event: React.MouseEvent) => {
    const target = event.target as HTMLElement;
    const x = parseInt(target.dataset.x || '0', 10);
    const y = parseInt(target.dataset.y || '0', 10);

    if (!isNaN(x) && !isNaN(y)) {
      const coordinate = { x, y };

      // 更新状态
      selectCell(x, y, event.ctrlKey || event.metaKey);

      // 创建交互事件
      const interactionEvent = createInteractionEvent('click', coordinate, {
        ctrl: event.ctrlKey,
        shift: event.shiftKey,
        alt: event.altKey,
      });

      // 处理业务逻辑
      const cell = matrixData.cells.get(`${x},${y}`);
      if (cell) {
        matrixCore.handleInteraction(interactionEvent, cell, finalConfig);
      }

      // 调用外部回调
      onCellClick?.(coordinate, event);
    }
  }, [matrixData.cells, finalConfig, selectCell, onCellClick]);

  // 处理双击
  const handleCellDoubleClick = useCallback((event: React.MouseEvent) => {
    const target = event.target as HTMLElement;
    const x = parseInt(target.dataset.x || '0', 10);
    const y = parseInt(target.dataset.y || '0', 10);

    if (!isNaN(x) && !isNaN(y)) {
      const coordinate = { x, y };
      onCellDoubleClick?.(coordinate, event);
    }
  }, [onCellDoubleClick]);

  // 处理悬停
  const handleCellMouseEnter = useCallback((event: React.MouseEvent) => {
    const target = event.target as HTMLElement;
    const x = parseInt(target.dataset.x || '0', 10);
    const y = parseInt(target.dataset.y || '0', 10);

    if (!isNaN(x) && !isNaN(y)) {
      const coordinate = { x, y };
      hoverCell(x, y);
      onCellHover?.(coordinate, event);
    }
  }, [hoverCell, onCellHover]);

  // 处理焦点
  const handleCellFocus = useCallback((event: React.FocusEvent) => {
    const target = event.target as HTMLElement;
    const x = parseInt(target.dataset.x || '0', 10);
    const y = parseInt(target.dataset.y || '0', 10);

    if (!isNaN(x) && !isNaN(y)) {
      const coordinate = { x, y };
      focusCell(x, y);
      onCellFocus?.(coordinate, event);
    }
  }, [focusCell, onCellFocus]);



  // 矩阵实际大小常量 - 根据33px格子+1px间距=34px计算，加2px缓冲
  const MATRIX_ACTUAL_SIZE = MATRIX_SIZE * 34; // 33 * 34 = 1122px，移除边框后直接匹配矩阵内容尺寸

  // 视口样式（外层滚动容器）- 响应式设计，保持1:1比例
  const viewportStyle: React.CSSProperties = {
    width: '100%',
    height: '100%',
    maxWidth: `${MATRIX_ACTUAL_SIZE}px`,
    maxHeight: `${MATRIX_ACTUAL_SIZE}px`,
    aspectRatio: '1 / 1', // 保持1:1比例
    overflow: 'auto',
    // 移除边框以避免占用内部空间，确保矩阵完全显示
    borderRadius: '6px', // 与格子统一的圆角
    ...style,
  };

  // 容器样式（内层矩阵容器）- 保持固定尺寸，允许滚动
  const containerStyle: React.CSSProperties = {
    position: 'relative',
    width: `${MATRIX_ACTUAL_SIZE}px`,
    height: `${MATRIX_ACTUAL_SIZE}px`,
    userSelect: 'none',
    minWidth: `${MATRIX_ACTUAL_SIZE}px`,
    minHeight: `${MATRIX_ACTUAL_SIZE}px`,
  };



  // 在客户端渲染完成前显示占位符
  if (!isClient) {
    return (
      <div
        className={`matrix-viewport ${className}`}
        style={{ ...viewportStyle, display: 'flex', alignItems: 'center', justifyContent: 'center' }}
      >
        <div className="text-gray-500">矩阵加载中...</div>
      </div>
    );
  }

  return (
    <div
      className={`matrix-viewport ${className}`}
      style={viewportStyle}
    >
      <div
        ref={containerRef}
        className="matrix-container"
        style={containerStyle}
        onClick={handleCellClick}
        onDoubleClick={handleCellDoubleClick}
        onMouseEnter={handleCellMouseEnter}
        onFocus={handleCellFocus}
        tabIndex={0}
        role="grid"
        aria-label="矩阵网格"
        aria-rowcount={33}
        aria-colcount={33}
      >
        {renderMatrixCells()}
      </div>
    </div>
  );
};

// ===== 性能优化 =====

const Matrix = memo(MatrixComponent, (prevProps, nextProps) => {
  // 简化的比较函数
  return (
    prevProps.configOverride === nextProps.configOverride &&
    prevProps.className === nextProps.className
  );
});

Matrix.displayName = 'Matrix';

export default Matrix;
